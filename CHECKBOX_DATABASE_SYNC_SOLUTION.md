# Solusi <PERSON>kron<PERSON>si Checkbox dengan Database

## <PERSON><PERSON><PERSON>, data yang diinsert ke database tidak sesuai dengan state checkbox yang terlihat di UI setelah user melakukan pagination atau search. Hal ini terjadi karena:

1. **Checkbox states hilang** saat pagination/search karena data table di-reload dari server
2. **Form submission hanya mengirim** checkbox yang tercentang di DOM saat submit, bukan state yang sebenarnya dari user interaction
3. **Database insertion tidak akurat** karena tidak mempertimbangkan user interactions (selected/unselected arrays)

## Solusi yang Diimplementasikan

### 1. Frontend (JavaScript) - Form Submission Enhancement

**File yang dimodifikasi:**
- `application/views/farmasi/formulir.php`
- `application/views/farmasi/editLayanan.php`

**Perubahan:**
```javascript
// Menambahkan selected/unselected arrays ke form data sebelum submit
formData.append('selectedResep', JSON.stringify(selectedResep));
formData.append('unselectedResep', JSON.stringify(unselectedResep));
formData.append('selectedPK', JSON.stringify(selectedPK));
formData.append('unselectedPK', JSON.stringify(unselectedPK));
formData.append('selectedDataLama', JSON.stringify(selectedDataLama)); // hanya editLayanan.php
formData.append('unselectedDataLama', JSON.stringify(unselectedDataLama)); // hanya editLayanan.php
formData.append('selectedSito', JSON.stringify(selectedSito));
formData.append('unselectedSito', JSON.stringify(unselectedSito));
formData.append('selectedHisto', JSON.stringify(selectedHisto));
formData.append('unselectedHisto', JSON.stringify(unselectedHisto));
formData.append('selectedImuno', JSON.stringify(selectedImuno));
formData.append('unselectedImuno', JSON.stringify(unselectedImuno));
formData.append('selectedMole', JSON.stringify(selectedMole));
formData.append('unselectedMole', JSON.stringify(unselectedMole));
formData.append('selectedRadiologi1', JSON.stringify(selectedRadiologi1));
formData.append('unselectedRadiologi1', JSON.stringify(unselectedRadiologi1));
formData.append('selectedRadiologi2', JSON.stringify(selectedRadiologi2));
formData.append('unselectedRadiologi2', JSON.stringify(unselectedRadiologi2));
```

### 2. Backend (PHP Controller) - Data Processing Logic

**File yang dimodifikasi:**
- `application/controllers/Farmasi.php`

**Method baru yang ditambahkan:**
```php
private function processFinalCheckboxState($originalData, $selectedArray, $unselectedArray)
{
    // Start with original data (items that were checked by default)
    $finalData = $originalData ?: [];
    
    // Add explicitly selected items
    foreach ($selectedArray as $item) {
        if (!in_array($item, $finalData)) {
            $finalData[] = $item;
        }
    }
    
    // Remove explicitly unselected items
    $finalData = array_filter($finalData, function($item) use ($unselectedArray) {
        return !in_array($item, $unselectedArray);
    });
    
    // Re-index array to ensure clean numeric indices
    return array_values($finalData);
}
```

**Perubahan di method `submitFormulir()`:**
```php
// Get selected/unselected arrays from form submission
$selectedResep = json_decode($this->input->post('selectedResep'), true) ?: [];
$unselectedResep = json_decode($this->input->post('unselectedResep'), true) ?: [];
$selectedPK = json_decode($this->input->post('selectedPK'), true) ?: [];
$unselectedPK = json_decode($this->input->post('unselectedPK'), true) ?: [];
$selectedDataLama = json_decode($this->input->post('selectedDataLama'), true) ?: [];
$unselectedDataLama = json_decode($this->input->post('unselectedDataLama'), true) ?: [];
$selectedSito = json_decode($this->input->post('selectedSito'), true) ?: [];
$unselectedSito = json_decode($this->input->post('unselectedSito'), true) ?: [];
$selectedHisto = json_decode($this->input->post('selectedHisto'), true) ?: [];
$unselectedHisto = json_decode($this->input->post('unselectedHisto'), true) ?: [];
$selectedImuno = json_decode($this->input->post('selectedImuno'), true) ?: [];
$unselectedImuno = json_decode($this->input->post('unselectedImuno'), true) ?: [];
$selectedMole = json_decode($this->input->post('selectedMole'), true) ?: [];
$unselectedMole = json_decode($this->input->post('unselectedMole'), true) ?: [];
$selectedRadiologi1 = json_decode($this->input->post('selectedRadiologi1'), true) ?: [];
$unselectedRadiologi1 = json_decode($this->input->post('unselectedRadiologi1'), true) ?: [];
$selectedRadiologi2 = json_decode($this->input->post('selectedRadiologi2'), true) ?: [];
$unselectedRadiologi2 = json_decode($this->input->post('unselectedRadiologi2'), true) ?: [];

// Process final checkbox states based on user interactions
$finalResep = $this->processFinalCheckboxState($resep, $selectedResep, $unselectedResep);
$finalPK = $this->processFinalCheckboxState($input_pk, $selectedPK, $unselectedPK);
$finalDataLama = $this->processFinalCheckboxState($inputOldData, $selectedDataLama, $unselectedDataLama);
$finalSitologi = $this->processFinalCheckboxState($sitologi, $selectedSito, $unselectedSito);
$finalHistologi = $this->processFinalCheckboxState($histologi, $selectedHisto, $unselectedHisto);
$finalImunohistokimia = $this->processFinalCheckboxState($imunohistokimia, $selectedImuno, $unselectedImuno);
$finalMolekuler = $this->processFinalCheckboxState($molekuler, $selectedMole, $unselectedMole);
$finalRadiologi1 = $this->processFinalCheckboxState($radiologi_verif, $selectedRadiologi1, $unselectedRadiologi1);
$finalRadiologi2 = $this->processFinalCheckboxState($radiologi_layanan, $selectedRadiologi2, $unselectedRadiologi2);

// Menggunakan $finalResep, $finalPK, $finalDataLama, dll. untuk database insertion
```

## Cara Kerja Solusi

### 1. User Interaction Tracking
- Array `selectedXxx` menyimpan ID item yang explicitly dipilih user
- Array `unselectedXxx` menyimpan ID item yang explicitly di-uncheck user
- Arrays ini tetap konsisten meskipun terjadi pagination/search

### 2. Final State Processing
Saat form submission, method `processFinalCheckboxState()` menggabungkan:
- **Original data**: Checkbox yang sudah tercentang dari database
- **Selected items**: Item yang explicitly dipilih user
- **Unselected items**: Item yang explicitly di-uncheck user

### 3. Database Insertion
Data final yang sudah diproses digunakan untuk database insertion, memastikan konsistensi antara UI dan database.

## Test Cases yang Berhasil

1. ✅ **User menambah selection**: Original [1,2] + Selected [3,4] = Result [1,2,3,4]
2. ✅ **User menghapus selection**: Original [1,2,3] - Unselected [2] = Result [1,3]
3. ✅ **User menambah dan menghapus**: Original [1,2] + Selected [3,4] - Unselected [1] = Result [2,3,4]
4. ✅ **Empty original data**: Original [] + Selected [1,2] = Result [1,2]
5. ✅ **Null original data**: Original null + Selected [1,2] = Result [1,2]

## Keuntungan Solusi

1. **Konsistensi UI-Database**: Data yang diinsert sesuai dengan yang terlihat di UI
2. **Backward Compatible**: Tidak merusak fungsionalitas yang sudah ada
3. **Robust**: Menangani berbagai edge cases (null data, empty arrays, dll.)
4. **Maintainable**: Logika terpusat dalam satu method yang mudah dipahami

## File yang Tidak Perlu Diubah

- `application/views/farmasi/detailLayanan.php` - Halaman view-only tanpa form submission

## Checkbox Arrays yang Ditangani

### formulir.php dan editLayanan.php:
- **Resep**: `selectedResep`, `unselectedResep`
- **PK (Hasil PK)**: `selectedPK`, `unselectedPK`
- **Sitologi**: `selectedSito`, `unselectedSito`
- **Histologi**: `selectedHisto`, `unselectedHisto`
- **Imunohistokimia**: `selectedImuno`, `unselectedImuno`
- **Molekuler**: `selectedMole`, `unselectedMole`
- **Radiologi Verifikasi**: `selectedRadiologi1`, `unselectedRadiologi1`
- **Radiologi Layanan**: `selectedRadiologi2`, `unselectedRadiologi2`

### Khusus editLayanan.php:
- **Data Lama**: `selectedDataLama`, `unselectedDataLama`

## Perbaikan JavaScript Scope Issue

**Masalah yang ditemukan:**
- Error `selectedResep is not defined` karena variabel dideklarasikan di dalam function scope tetapi diakses di luar scope

**Solusi yang diterapkan:**
- Memindahkan semua deklarasi variabel checkbox (`selectedXxx`, `unselectedXxx`) ke scope global
- Menghapus deklarasi duplikat yang ada di berbagai tempat dalam file
- Memastikan variabel dapat diakses dari form submission handler

**File yang diperbaiki:**
- `application/views/farmasi/formulir.php` - Scope variabel diperbaiki
- `application/views/farmasi/editLayanan.php` - Scope variabel diperbaiki

## Implementasi Selesai

Solusi ini telah diimplementasikan dan siap digunakan. Semua checkbox arrays (termasuk radiologi dan molekuler) sekarang akan tetap konsisten antara UI dan database insertion, menyelesaikan masalah yang dilaporkan user. JavaScript scope issues juga telah diperbaiki.
